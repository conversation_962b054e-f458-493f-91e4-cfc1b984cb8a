CREATE PROC dbo.swb_updateBundleSettings
@bundleID INT,
@siteCode VARCHAR(10),
@customTextEnabled BIT,
@customTextContent VARCHAR(MAX) = NULL,
@recordedByMemberID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @participantID INT, @orgID INT, @siteID INT, @msgjson VARCHAR(MAX);

	SELECT @participantID = dbo.fn_getParticipantIDFromOrgcode(@sitecode);
	SELECT @orgID = orgID, @siteID = siteID FROM membercentral.dbo.sites WHERE siteCode = @sitecode;

	-- Validate bundle exists and belongs to the organization
	IF NOT EXISTS (
		SELECT 1 FROM dbo.tblBundles b
		INNER JOIN dbo.tblParticipants p ON b.participantID = p.participantID
		WHERE b.bundleID = @bundleID AND p.orgcode = @siteCode
	)
	BEGIN
		RAISERROR('Bundle not found or access denied.', 16, 1);
		RETURN -1;
	END

	-- Update bundle settings
	UPDATE dbo.tblBundles
	SET customTextEnabled = @customTextEnabled,
		customTextContent = CASE WHEN @customTextEnabled = 1 THEN @customTextContent ELSE NULL END
	WHERE bundleID = @bundleID;

	-- Log audit trail
	SET @msgjson = '{"bundleID":' + CAST(@bundleID AS VARCHAR) + ',"customTextEnabled":' + CAST(@customTextEnabled AS VARCHAR) + ',"customTextLength":' + CAST(LEN(ISNULL(@customTextContent, '')) AS VARCHAR) + '}';
	EXEC membercentral.dbo.ams_addAuditLogEntry @memberID=@recordedByMemberID, @orgID=@orgID, @siteID=@siteID, @auditType='SWB Settings Update', @auditDesc='Bundle settings updated', @jsonData=@msgjson;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
