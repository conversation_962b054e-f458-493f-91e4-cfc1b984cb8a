ALTER PROC dbo.swb_updateBundle
@bundleID INT,
@siteCode VARCHAR(10),
@bundleName VARCHAR(250),
@bundleSubTitle VARCHAR(250),
@programCode VARCHAR(15),
@bundleDesc VARCHAR(MAX),
@status CHAR(1),
@dateActivated DATE,
@lockSettings BIT,
@recordedByMemberID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @participantID INT, @updateSearchText BIT = 0, @orgID INT, @siteID INT, @crlf VARCHAR(10), @msgjson VARCHAR(MAX);

	SET @crlf = CHAR(13) + CHAR(10);
	SELECT @participantID = dbo.fn_getParticipantIDFromOrgcode(@sitecode);
	SELECT @orgID = orgID, @siteID = siteID FROM membercentral.dbo.sites WHERE siteCode = @sitecode;

	IF OBJECT_ID('tempdb..#tblExistingBundle') IS NOT NULL
		DROP TABLE #tblExistingBundle;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;
	CREATE TABLE #tblExistingBundle(bundleID int, bundleName VARCHAR(250), bundleSubTitle VARCHAR(250), programCode VARCHAR(15), bundleDesc VARCHAR(MAX), 
		[status] CHAR(1), dateActivated DATE, lockSettings BIT);
	CREATE TABLE #tmpLogMessages (rowID INT IDENTITY(1,1), msg VARCHAR(MAX));

	IF NULLIF(@programCode,'') IS NULL OR dbo.fn_sw_isUniqueProgramCode('SWB',@bundleID,@programCode) = 0
		RAISERROR('Program Code must be unique.', 16, 1);

	INSERT INTO #tblExistingBundle(bundleID, bundleName, bundleSubTitle, programCode, bundleDesc, [status], dateActivated, lockSettings)
	SELECT b.bundleID, b.bundleName, b.bundleSubTitle, b.programCode, b.bundleDesc, b.[status], b.dateActivated, b.lockSettings
	FROM dbo.tblBundles AS b
	WHERE b.bundleID = @bundleID;

	IF NOT EXISTS (
		SELECT bundleID
		FROM dbo.tblBundles
		WHERE bundleID = @bundleID
		AND bundleName = @bundleName
		AND ISNULL(bundleSubTitle,'') = ISNULL(@bundleSubTitle,'')
		AND bundleDesc = @bundleDesc
	)
		SET @updateSearchText = 1;

	BEGIN TRAN;
		UPDATE dbo.tblBundles
		SET bundleName = @bundleName,
			bundleSubTitle = @bundleSubTitle,
			programCode = @programCode,
			bundleDesc = @bundleDesc,
			[status] = @status,
			dateActivated = @dateActivated,
			lockSettings = ISNULL(@lockSettings,lockSettings)
		WHERE bundleID = @bundleID;

		IF @updateSearchText = 1
			EXEC dbo.swb_populateSearchText @bundleID = @bundleID;
	COMMIT TRAN;

	/* audit log */
	INSERT INTO #tmpLogMessages(msg)
	SELECT 'Bundle Title changed from [' + bundleName + '] to [' + @bundleName + '].'
	FROM #tblExistingBundle
	WHERE bundleID = @bundleID
	AND bundleName <> @bundleName;

	INSERT INTO #tmpLogMessages(msg)
	SELECT 'Bundle Sub Title changed from [' + ISNULL(NULLIF(bundleSubTitle,''),'blank') + '] to [' + ISNULL(NULLIF(@bundleSubTitle,''),'blank') + '].'
	FROM #tblExistingBundle
	WHERE bundleID = @bundleID
	AND ISNULL(bundleSubTitle,'') <> ISNULL(@bundleSubTitle,'');

	INSERT INTO #tmpLogMessages(msg)
	SELECT 'Program Code changed from [' + ISNULL(NULLIF(programCode,''),'blank') + '] to [' + ISNULL(NULLIF(@programCode,''),'blank') + '].'
	FROM #tblExistingBundle
	WHERE bundleID = @bundleID
	AND programCode <> @programCode;

	INSERT INTO #tmpLogMessages(msg)
	SELECT 'Description changed from [' + ISNULL(NULLIF(bundleDesc,''),'blank') + '] to [' + ISNULL(NULLIF(@bundleDesc,''),'blank') + '].'
	FROM #tblExistingBundle
	WHERE bundleID = @bundleID
	AND bundleDesc <> @bundleDesc;

	INSERT INTO #tmpLogMessages(msg)
	SELECT 'Status changed from ' + CASE WHEN [status] = 'A' THEN 'Active' ELSE 'Inactive' END + ' to ' + CASE WHEN @status = 'A' THEN 'Active' ELSE 'Inactive' END + '.'
	FROM #tblExistingBundle
	WHERE bundleID = @bundleID
	AND [status] <> @status;

	INSERT INTO #tmpLogMessages(msg)
	SELECT 'Activated Date changed from ' + ISNULL(CONVERT(VARCHAR(20),dateActivated,120),'[blank]') + ' to ' + ISNULL(CONVERT(VARCHAR(20),@dateActivated,120),'[blank]') + '.'
	FROM #tblExistingBundle
	WHERE bundleID = @bundleID
	AND ISNULL(dateActivated,'') <> ISNULL(@dateActivated,'');

	INSERT INTO #tmpLogMessages(msg)
	SELECT 'Lock Settings changed from ' + CASE WHEN lockSettings = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @lockSettings = 1 THEN 'Yes' ELSE 'No' END + '.'
	FROM #tblExistingBundle
	WHERE bundleID = @bundleID
	AND lockSettings <> @lockSettings;

	SELECT @msgjson = 'SWB-' + CAST(bundleID AS VARCHAR(10)) + ' [' + memberCentral.dbo.fn_cleanInvalidXMLChars(bundleName) + '] has been updated.'
	FROM #tblExistingBundle
	WHERE bundleID = @bundleID;

	IF EXISTS(SELECT 1 FROM #tmpLogMessages) BEGIN
		SET @msgjson = @msgjson + @crlf + 'The following changes have been made:';

		SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + memberCentral.dbo.fn_cleanInvalidXMLChars(msg)
		FROM #tmpLogMessages
		WHERE msg IS NOT NULL;
	END

	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	VALUES ('{ "c":"auditLog", "d": {
		"AUDITCODE":"SW",
		"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
		"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
		"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
		"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
		"MESSAGE":"' + REPLACE(@msgjson,'"','\"') + '" } }');

	IF OBJECT_ID('tempdb..#tblExistingBundle') IS NOT NULL
		DROP TABLE #tblExistingBundle;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
