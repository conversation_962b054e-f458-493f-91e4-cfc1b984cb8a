<cfoutput>
<form name="frmSWBProgramSettings" id="frmSWBProgramSettings" class="mb-3">
<input type="hidden" name="bundleID" id="bundleID" value="#local.qryBundle.bundleID#">

<div id="err_swbsettings" class="alert alert-danger mb-2 d-none"></div>

<div class="card">
	<div class="card-header">
		<div class="card-header--title">
		<b>At Time of Registration</b>
		</div>
	</div>
	<div class="card-body">
		<div class="form-group row">
			<div class="col-sm-12">
				<div class="custom-control custom-switch">
					<input type="checkbox" name="customTextEnabled" id="customTextEnabled" value="1" class="custom-control-input"<cfif local.qryBundle.customTextEnabled is 1> checked="checked"</cfif> onchange="toggleCustomTextArea();">
					<label class="custom-control-label" for="customTextEnabled">Include custom text in confirmation emails</label>
				</div>
				<small class="form-text text-muted">When enabled, custom text will appear in SWL and SWOD confirmation emails above the signature line.</small>
			</div>
		</div>
		
		<div class="form-group row mt-3" id="customTextGroup"<cfif local.qryBundle.customTextEnabled is not 1> style="display:none;"</cfif>>
			<div class="col-sm-12">
				<div class="form-label-group">
					<textarea name="customTextContent" id="customTextContent" class="form-control" rows="4" maxlength="2000" placeholder="Enter custom text to appear in confirmation emails...">#encodeForHTML(local.qryBundle.customTextContent)#</textarea>
					<label for="customTextContent">Custom Text Content</label>
				</div>
				<small class="form-text text-muted">Maximum 2000 characters. This text will appear in confirmation emails for all programs included in this bundle.</small>
			</div>
		</div>
	</div>
</div>

</form>

<div id="divSWProgramSettingsSaveLoading" style="display:none;">
	<div class="text-center">
		<br/>
		<i class="fa-light fa-circle-notch fa-spin fa-3x"></i>
		<br/><br/>
		<b>Please wait while we save these settings.</b>
		<br/>
	</div>
</div>

<script type="text/javascript">
function toggleCustomTextArea() {
	var isEnabled = $('##customTextEnabled').is(':checked');
	if (isEnabled) {
		$('##customTextGroup').slideDown();
		$('##customTextContent').focus();
	} else {
		$('##customTextGroup').slideUp();
		$('##customTextContent').val('');
	}
}
</script>
</cfoutput>
