USE seminarWeb
GO

-- Add custom text fields to tblBundles table
ALTER TABLE dbo.tblBundles 
ADD customTextEnabled BIT NOT NULL DEFAULT(0),
    customTextContent VARCHAR(MAX) NULL;
GO

-- Update sw_getBundleByBundleID to include new fields
ALTER PROC dbo.sw_getBundleByBundleID
@bundleID INT,
@orgcode VARCHAR(10)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SELECT TOP 1 b.bundleID, b.isSWOD, p.orgcode, b.bundleName, b.bundleSubTitle, b.bundleDesc, b.programCode, b.isPriceBasedOnActual,
		b.allowSyndication, b.priceSyndication, b.dateCatalogStart, b.dateCatalogEnd, b.[status], b.includeSpeakers, p.orgcode AS publisherOrgCode,
		b.revenueGLAccountID, b.pushDefaultPricingToOptIns, b.allowOptInRateChange, p.handlesOwnPayment, b.freeRateDisplay, b.dateActivated, b.lockSettings,
		b.customTextEnabled, b.customTextContent,
		mActive.memberID as submittedByMemberID, mActive.firstname AS submitterFirstName, mActive.firstname + ' ' + mActive.lastname AS submittedByMember, me.email as submittedByEmail, mcs.siteID AS participantSiteID
	FROM dbo.tblBundles AS b 
	INNER JOIN dbo.tblParticipants AS p ON b.participantID = p.participantID
	INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
	LEFT JOIN memberCentral.dbo.ams_members as m on m.memberID = b.submittedByMemberID
	LEFT JOIN memberCentral.dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
	LEFT JOIN memberCentral.dbo.ams_memberEmails as me on me.memberID = mActive.memberID AND me.orgID in (mcs.orgID,1)
	LEFT JOIN memberCentral.dbo.ams_memberEmailTags AS metag ON metag.orgID = m.orgID AND metag.memberID = me.memberID AND metag.emailTypeID = me.emailTypeID
	LEFT JOIN memberCentral.dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = m.orgID AND metagt.emailTagTypeID = metag.emailTagTypeID AND metagt.emailTagType = 'Primary'
	INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.[State] = p.orgcode
	WHERE b.bundleID = @bundleID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- Update sw_addBundle to handle new fields
ALTER PROC dbo.sw_addBundle
@orgcode varchar(10),
@isSWOD bit,
@bundleName varchar(200),
@bundleSubTitle varchar(200),
@bundleDesc varchar(max),
@dateCatalogStart smalldatetime,
@dateCatalogEnd smalldatetime,
@status char(1),
@freeRateDisplay varchar(5),
@isPriceBasedOnActual bit,
@customTextEnabled bit = 0,
@customTextContent varchar(max) = NULL,
@recordedByMemberID int,
@bundleID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @participantID int, @orgID int, @siteID int, @programCode varchar(15), @nowDate datetime = getdate(), 
		@resourceTypeID int, @siteResourceID int, @semWebCatalogSiteResourceID int;

	SELECT @resourceTypeID = memberCentral.dbo.fn_getResourceTypeID('SWBundle');
	SET @participantID = dbo.fn_getParticipantIDFromOrgCode(@orgcode);
	SELECT @orgID = orgID, @siteID = siteID FROM membercentral.dbo.sites WHERE siteCode = @orgcode;
	SET @semWebCatalogSiteResourceID = membercentral.dbo.fn_getSiteResourceIDForResourceType('SemWebCatalog', @siteID);
	
	EXEC memberCentral.dbo.getUniqueCode @uniqueCode=@programCode OUTPUT;

	BEGIN TRAN;
		EXEC memberCentral.dbo.cms_createSiteResource @resourceTypeID=@resourceTypeID, @siteResourceStatusID=1, @siteID=@siteID,  
			@isVisible=1, @parentSiteResourceID=@semWebCatalogSiteResourceID, @siteResourceID=@siteResourceID OUTPUT;
			
		INSERT INTO dbo.tblBundles (participantID, isSWOD, bundleName, bundleSubTitle, programCode, bundleDesc, dateCatalogStart, dateCatalogEnd,
			[status], dateCreated, isPriceBasedOnActual, freeRateDisplay, siteResourceID, submittedByMemberID, customTextEnabled, customTextContent)
		VALUES (@participantID, @isSWOD, @bundleName, @bundleSubTitle, @programCode, @bundleDesc, @dateCatalogStart, @dateCatalogEnd, @status,
			@nowDate, @isPriceBasedOnActual, @freeRateDisplay, @siteResourceID, @recordedByMemberID, @customTextEnabled, 
			CASE WHEN @customTextEnabled = 1 THEN @customTextContent ELSE NULL END);
		SELECT @bundleID = SCOPE_IDENTITY();

		SET @programCode = 'SWB-' + CAST(@bundleID AS varchar(10));

		UPDATE dbo.tblBundles
		SET programCode = @programCode
		WHERE bundleID = @bundleID;

		INSERT INTO dbo.tblBundlesOptIn (bundleID, participantID, isPriceBasedOnActual, freeRateDisplay, dateAdded, isActive)
		VALUES (@bundleID, @participantID, @isPriceBasedOnActual, @freeRateDisplay, @nowDate, 1);

		EXEC dbo.swb_addBillingLogForBundleCreation @orgCode=@orgCode, @bundleID=@bundleID, @recordedByMemberID=@recordedByMemberID;

		INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
		SELECT '{ "c":"auditLog", "d": {
			"AUDITCODE":"SW",
			"ORGID":' + cast(@orgID as varchar(10)) + ',
			"SITEID":' + cast(@siteID as varchar(10)) + ',
			"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
			"ACTIONDATE":"' + convert(varchar(20),@nowDate,120) + '",
			"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars('SWB-' + cast(bundleID as varchar(20)) + ' [' + bundleName + '] has been created as ['+ CASE WHEN @isSWOD = 1 THEN 'SWOD' ELSE 'SWL' END +'].'),'"','\"') + '" } }'
		FROM dbo.tblBundles
		WHERE bundleID = @bundleID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- Create dedicated stored procedure for updating SWB settings
CREATE PROC dbo.swb_updateBundleSettings
@bundleID INT,
@siteCode VARCHAR(10),
@customTextEnabled BIT,
@customTextContent VARCHAR(MAX) = NULL,
@recordedByMemberID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @participantID INT, @orgID INT, @siteID INT, @msgjson VARCHAR(MAX), @crlf VARCHAR(10);

	SET @crlf = CHAR(13) + CHAR(10);
	SELECT @participantID = dbo.fn_getParticipantIDFromOrgcode(@sitecode);
	SELECT @orgID = orgID, @siteID = siteID FROM membercentral.dbo.sites WHERE siteCode = @sitecode;

	-- Create temp tables for logging (following swb_updateBundle pattern)
	IF OBJECT_ID('tempdb..#tblExistingBundleSettings') IS NOT NULL
		DROP TABLE #tblExistingBundleSettings;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;
	CREATE TABLE #tblExistingBundleSettings(bundleID INT, bundleName VARCHAR(250), customTextEnabled BIT, customTextContent VARCHAR(MAX));
	CREATE TABLE #tmpLogMessages (rowID INT IDENTITY(1,1), msg VARCHAR(MAX));

	-- Validate bundle exists and belongs to the organization
	IF NOT EXISTS (
		SELECT 1 FROM dbo.tblBundles b
		INNER JOIN dbo.tblParticipants p ON b.participantID = p.participantID
		WHERE b.bundleID = @bundleID AND p.orgcode = @siteCode
	)
	BEGIN
		RAISERROR('Bundle not found or access denied.', 16, 1);
		RETURN -1;
	END

	-- Store existing values for audit logging
	INSERT INTO #tblExistingBundleSettings(bundleID, bundleName, customTextEnabled, customTextContent)
	SELECT bundleID, bundleName, ISNULL(customTextEnabled, 0), customTextContent
	FROM dbo.tblBundles
	WHERE bundleID = @bundleID;

	-- Update bundle settings
	UPDATE dbo.tblBundles
	SET customTextEnabled = @customTextEnabled,
		customTextContent = CASE WHEN @customTextEnabled = 1 THEN @customTextContent ELSE NULL END
	WHERE bundleID = @bundleID;

	INSERT INTO #tmpLogMessages(msg)
	SELECT 'Custom Text Enabled changed from ' + CASE WHEN customTextEnabled = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @customTextEnabled = 1 THEN 'Yes' ELSE 'No' END + '.'
	FROM #tblExistingBundleSettings
	WHERE bundleID = @bundleID
	AND customTextEnabled <> @customTextEnabled;

	INSERT INTO #tmpLogMessages(msg)
	SELECT 'Custom Text Content ' + CASE WHEN @customTextEnabled = 1 THEN 'updated' ELSE 'cleared' END + '.'
	FROM #tblExistingBundleSettings
	WHERE bundleID = @bundleID
	AND (
		(@customTextEnabled = 1 AND ISNULL(customTextContent,'') <> ISNULL(@customTextContent,''))
		OR (@customTextEnabled = 0 AND customTextContent IS NOT NULL)
	);

	SELECT @msgjson = 'SWB-' + CAST(bundleID AS VARCHAR(10)) + ' [' + memberCentral.dbo.fn_cleanInvalidXMLChars(bundleName) + '] settings have been updated.'
	FROM #tblExistingBundleSettings
	WHERE bundleID = @bundleID;

	IF EXISTS(SELECT 1 FROM #tmpLogMessages) BEGIN
		SET @msgjson = @msgjson + @crlf + 'The following changes have been made:';

		SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + memberCentral.dbo.fn_cleanInvalidXMLChars(msg)
		FROM #tmpLogMessages
		WHERE msg IS NOT NULL;
	END

	-- Log to platformQueue.dbo.queue_mongo (same as swb_updateBundle)
	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	VALUES ('{ "c":"auditLog", "d": {
		"AUDITCODE":"SW",
		"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
		"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
		"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
		"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
		"MESSAGE":"' + REPLACE(@msgjson,'"','\"') + '" } }');

	-- Clean up temp tables
	IF OBJECT_ID('tempdb..#tblExistingBundleSettings') IS NOT NULL
		DROP TABLE #tblExistingBundleSettings;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

USE membercentral;
GO

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @componentID int, @methodID int, @editRTFID int, @publishRTFID int;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;
	CREATE TABLE #ajaxComponentMethods (autoid int IDENTITY(1,1), methodName varchar(500), resourceTypeFunctionID int, methodID int);
	SELECT @editRTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('SeminarWebAdmin'),dbo.fn_getResourceFunctionID('editBundleAll',dbo.fn_getResourceTypeID('SeminarWebAdmin')));
	SELECT @publishRTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('SeminarWebAdmin'),dbo.fn_getResourceFunctionID('editBundlePublish',dbo.fn_getResourceTypeID('SeminarWebAdmin')));
	
	INSERT INTO #ajaxComponentMethods(methodName, resourceTypeFunctionID)
	VALUES 
		('updateSWBProgramSettings', @editRTFID), 
		('updateSWBProgramSettings', @publishRTFID);

	EXEC dbo.ajax_addComponentMethodRightsBulk
		@componentName='ADMINSWB',
		@requestCFC='model.admin.seminarWeb.seminarWebSWB',
		@componentID=@componentID OUTPUT;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	SELECT ERROR_MESSAGE();
END CATCH
GO