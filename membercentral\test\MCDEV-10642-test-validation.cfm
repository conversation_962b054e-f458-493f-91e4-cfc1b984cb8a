<!---
MCDEV-10642 Test Validation Script
This script validates the implementation of custom text functionality for SWB bundles.
Run this on ohca.test.mcinternal.com after deployment.
--->

<cfoutput>
<h2>MCDEV-10642: SWB Custom Text Feature - Test Validation</h2>

<h3>Database Schema Validation</h3>
<cftry>
	<cfquery name="qryTestSchema" datasource="#application.dsn.tlasites_seminarweb.dsn#">
		SELECT TOP 1 
			bundleID, 
			customTextEnabled, 
			customTextContent
		FROM dbo.tblBundles 
		WHERE bundleID > 0
	</cfquery>
	
	<div class="alert alert-success">
		✓ Database schema updated successfully. New fields are present:
		<ul>
			<li>customTextEnabled (BIT)</li>
			<li>customTextContent (VARCHAR(MAX))</li>
		</ul>
	</div>
<cfcatch>
	<div class="alert alert-danger">
		✗ Database schema validation failed: #cfcatch.message#
	</div>
</cfcatch>
</cftry>

<h3>Stored Procedure Validation</h3>
<cftry>
	<cfquery name="qryTestSP" datasource="#application.dsn.tlasites_seminarweb.dsn#">
		SELECT ROUTINE_NAME, ROUTINE_DEFINITION
		FROM INFORMATION_SCHEMA.ROUTINES
		WHERE ROUTINE_NAME IN ('swb_updateBundleSettings', 'sw_getBundleByBundleID', 'sw_addBundle')
		AND ROUTINE_DEFINITION LIKE '%customText%'
	</cfquery>

	<cfquery name="qryTestDedicatedSP" datasource="#application.dsn.tlasites_seminarweb.dsn#">
		SELECT ROUTINE_NAME
		FROM INFORMATION_SCHEMA.ROUTINES
		WHERE ROUTINE_NAME = 'swb_updateBundleSettings'
	</cfquery>

	<cfif qryTestDedicatedSP.recordCount gt 0>
		<div class="alert alert-success">
			✓ Dedicated settings stored procedure created successfully: swb_updateBundleSettings
		</div>
	<cfelse>
		<div class="alert alert-danger">
			✗ Dedicated settings stored procedure not found: swb_updateBundleSettings
		</div>
	</cfif>

	<div class="alert alert-success">
		✓ Found #qryTestSP.recordCount# procedures with custom text support.
	</div>
<cfcatch>
	<div class="alert alert-danger">
		✗ Stored procedure validation failed: #cfcatch.message#
	</div>
</cfcatch>
</cftry>

<h3>UI Component Validation</h3>
<cfset uiFilesExist = true>
<cfset uiFiles = [
	"membercentral/model/admin/seminarWeb/frm_SWB_program_settings.cfm",
	"membercentral/model/admin/seminarWeb/frm_SWB_program_overall.cfm"
]>

<cfloop array="#uiFiles#" index="filePath">
	<cfif NOT fileExists(expandPath("/#filePath#"))>
		<cfset uiFilesExist = false>
		<div class="alert alert-danger">✗ Missing UI file: #filePath#</div>
	</cfif>
</cfloop>

<cfif uiFilesExist>
	<div class="alert alert-success">✓ All UI components are present</div>
</cfif>

<h3>JavaScript Function Validation</h3>
<cfif fileExists(expandPath("/membercentral/assets/admin/javascript/seminarweb.js"))>
	<cffile action="read" file="#expandPath('/membercentral/assets/admin/javascript/seminarweb.js')#" variable="jsContent">
	<cfif findNoCase("validateAndSaveSWBProgramSettings", jsContent)>
		<div class="alert alert-success">✓ JavaScript validation function added successfully</div>
	<cfelse>
		<div class="alert alert-danger">✗ JavaScript validation function not found</div>
	</cfif>
<cfelse>
	<div class="alert alert-danger">✗ JavaScript file not found</div>
</cfif>

<h3>ColdFusion Component Validation</h3>
<cftry>
	<cfset testCFC = CreateObject("component", "model.admin.seminarWeb.seminarWebSWB")>
	<cfif structKeyExists(testCFC, "updateSWBProgramSettings")>
		<div class="alert alert-success">✓ ColdFusion component method added successfully</div>
	<cfelse>
		<div class="alert alert-danger">✗ ColdFusion component method not found</div>
	</cfif>
<cfcatch>
	<div class="alert alert-danger">✗ ColdFusion component validation failed: #cfcatch.message#</div>
</cfcatch>
</cftry>

<h3>Email Template Validation</h3>
<cfif fileExists(expandPath("/membercentral/model/seminarweb/SWBundlesEmails.cfc"))>
	<cffile action="read" file="#expandPath('/membercentral/model/seminarweb/SWBundlesEmails.cfc')#" variable="emailContent">
	<cfif findNoCase("customTextEnabled", emailContent) AND findNoCase("customTextContent", emailContent)>
		<div class="alert alert-success">✓ Email template updated successfully</div>
	<cfelse>
		<div class="alert alert-danger">✗ Email template not updated properly</div>
	</cfif>
<cfelse>
	<div class="alert alert-danger">✗ Email template file not found</div>
</cfif>

<h3>Test Instructions</h3>
<div class="alert alert-info">
	<h4>Manual Testing Steps:</h4>
	<ol>
		<li><strong>Create Test SWB:</strong>
			<ul>
				<li>Navigate to SeminarWeb Admin > Bundles</li>
				<li>Create a new bundle or edit existing one</li>
				<li>Go to Program tab</li>
			</ul>
		</li>
		<li><strong>Verify Settings Well:</strong>
			<ul>
				<li>Confirm "Settings" well appears after "Basics" well</li>
				<li>Verify description: "Customize bundle settings to guide registrants..."</li>
				<li>Check "At Time of Registration" card is present</li>
			</ul>
		</li>
		<li><strong>Test Toggle Functionality:</strong>
			<ul>
				<li>Toggle "Include custom text in confirmation emails" on/off</li>
				<li>Verify text area appears/disappears correctly</li>
				<li>Enter test custom text (max 2000 characters)</li>
				<li>Save settings</li>
			</ul>
		</li>
		<li><strong>Test Email Integration:</strong>
			<ul>
				<li>Complete a test registration for the bundle</li>
				<li>Check confirmation email contains custom text above signature</li>
				<li>Verify custom text appears in both HTML and receipt versions</li>
			</ul>
		</li>
		<li><strong>Test Validation:</strong>
			<ul>
				<li>Try entering more than 2000 characters</li>
				<li>Verify validation error appears</li>
				<li>Test with custom text disabled - should not appear in email</li>
			</ul>
		</li>
	</ol>
</div>

<h3>Expected Results</h3>
<div class="alert alert-success">
	<h4>Success Criteria:</h4>
	<ul>
		<li>✓ Settings Well appears after Basics Well</li>
		<li>✓ Toggle and text area function correctly</li>
		<li>✓ Custom text saves and loads properly</li>
		<li>✓ Custom text appears in confirmation emails above signature</li>
		<li>✓ Validation prevents text over 2000 characters</li>
		<li>✓ Feature works for both SWL and SWOD bundles</li>
		<li>✓ No impact on existing functionality</li>
	</ul>
</div>

<p><strong>Test Environment:</strong> ohca.test.mcinternal.com</p>
<p><strong>Test Date:</strong> #dateFormat(now(), "mm/dd/yyyy")# #timeFormat(now(), "h:mm tt")#</p>
</cfoutput>

<style>
.alert {
	padding: 15px;
	margin-bottom: 20px;
	border: 1px solid transparent;
	border-radius: 4px;
}
.alert-success {
	color: #3c763d;
	background-color: #dff0d8;
	border-color: #d6e9c6;
}
.alert-danger {
	color: #a94442;
	background-color: #f2dede;
	border-color: #ebccd1;
}
.alert-info {
	color: #31708f;
	background-color: #d9edf7;
	border-color: #bce8f1;
}
</style>
